using UnityEngine;
using UnityEngine.UI;

public class WeatherManager : MonoBehaviour
{
    public ParticleSystem rainEffect;
    public ParticleSystem airstormEffect;
    public Light directionalLight;
    public Button Sunny, Rain, Airstrom;
    public Material material1;
    public Texture sunnyTexture;
    public Texture rainyTexture;
    public Texture airstormTexture;

    //public AudioClip rainSound;
    public AudioClip airstormSound;
    public AudioSource audioSource;
    public GameObject text1, text2;
    public static WeatherManager instance;

    public enum WeatherType { Sunny, Rainy, Airstorm }

    void Start()
    {
        instance = this;
        SetWeather(WeatherType.Sunny);
        audioSource = gameObject.AddComponent<AudioSource>();
        UpdateButtonStates(WeatherType.Sunny);
        Sunny.transform.GetChild(0).gameObject.SetActive(false);
        Rain.transform.GetChild(0).gameObject.SetActive(true);
        Airstrom.transform.GetChild(0).gameObject.SetActive(true);

        // Setup button listeners
        SetupButtonListeners();
    }

    private void SetupButtonListeners()
    {
        if (Sunny != null)
            Sunny.onClick.AddListener(RewardAdSunny);
        if (Rain != null)
            Rain.onClick.AddListener(RewardAdRain);
        if (Airstrom != null)
            Airstrom.onClick.AddListener(RewardAdAirstorm);
    }

    private bool hasWatchedAdForSunny = false;
    private bool hasWatchedAdForRainy = false;

    private void SaveWeatherPreferences()
    {
        PlayerPrefs.SetInt("HasWatchedAdForSunny", hasWatchedAdForSunny ? 1 : 0);
        PlayerPrefs.SetInt("HasWatchedAdForRainy", hasWatchedAdForRainy ? 1 : 0);
        PlayerPrefs.Save();
    }

    private void LoadWeatherPreferences()
    {
        hasWatchedAdForSunny = PlayerPrefs.GetInt("HasWatchedAdForSunny", 0) == 1;
        hasWatchedAdForRainy = PlayerPrefs.GetInt("HasWatchedAdForRainy", 0) == 1;
    }

    private void SetTextInactiveStates()
    {
        if (hasWatchedAdForSunny)
        {
            text1.SetActive(false);
        }
        if (hasWatchedAdForRainy)
        {
            text2.SetActive(false);
        }
    }

    void Awake()
    {
        LoadWeatherPreferences();
        SetTextInactiveStates();
    }

    public void RewardAdSunny()
    {
        if (!hasWatchedAdForSunny)
        {
            AdsController.Instance.ShowRewardedInterstitialAd_Admob(() =>
            {
                SetSunnyWeather();
                hasWatchedAdForSunny = true;
                SaveWeatherPreferences();
                PlayerPrefs.SetInt("Settex1inactive", 1);
                PlayerPrefs.Save();
                SetTextInactiveStates();
            });
        }
        else
        {
            SetSunnyWeather();
        }
    }

    public void RewardAdRain()
    {
        if (!hasWatchedAdForRainy)
        {
            AdsController.Instance.ShowRewardedInterstitialAd_Admob(() =>
            {
                SetRainyWeather();
                hasWatchedAdForRainy = true;
                SaveWeatherPreferences();
                PlayerPrefs.SetInt("Settex2inactive", 1);
                PlayerPrefs.Save();
                SetTextInactiveStates();
            });
        }
        else
        {
            SetRainyWeather();
        }
    }

    public void RewardAdAirstorm()
    {
        // Airstorm is always available without ads
        SetAirstormWeather();
    }

    public void SetSunnyWeather()
    {
        SetWeather(WeatherType.Sunny);
        UpdateButtonStates(WeatherType.Sunny);
    }

    public void SetRainyWeather()
    {
        SetWeather(WeatherType.Rainy);
        UpdateButtonStates(WeatherType.Rainy);
    }

    public void SetAirstormWeather()
    {
        SetWeather(WeatherType.Airstorm);
        UpdateButtonStates(WeatherType.Airstorm);
    }

    private void SetWeather(WeatherType weatherType)
    {
        rainEffect.Stop();
        airstormEffect.Stop();
        audioSource.Stop();

        switch (weatherType)
        {
            case WeatherType.Sunny:
                directionalLight.intensity = 1f;
                RenderSettings.ambientSkyColor = Color.white;
                material1.mainTexture = sunnyTexture;
                break;

            case WeatherType.Rainy:
                directionalLight.intensity = 0.1f;
                RenderSettings.ambientSkyColor = new Color(145f / 255f, 145f / 255f, 145f / 255f);
                material1.mainTexture = rainyTexture;
                rainEffect.Play();
                //PlaySound(rainSound);
                break;

            case WeatherType.Airstorm:
                directionalLight.intensity = 0.3f;
                material1.mainTexture = airstormTexture;
                airstormEffect.Play();
                PlaySound(airstormSound);
                break;
        }

        DynamicGI.UpdateEnvironment();
    }

    private void PlaySound(AudioClip clip)
    {
        if (clip != null)
        {
            audioSource.clip = clip;
            audioSource.loop = true;
            audioSource.Play();
        }
    }

    private void UpdateButtonStates(WeatherType currentWeather)
    {
        if (currentWeather == WeatherType.Sunny)
        {
            Sunny.transform.GetChild(0).gameObject.SetActive(false);
            Rain.transform.GetChild(0).gameObject.SetActive(true);
            Airstrom.transform.GetChild(0).gameObject.SetActive(true);
        }
        else if (currentWeather == WeatherType.Rainy)
        {
            Sunny.transform.GetChild(0).gameObject.SetActive(true);
            Rain.transform.GetChild(0).gameObject.SetActive(false);
            Airstrom.transform.GetChild(0).gameObject.SetActive(true);
        }
        else if (currentWeather == WeatherType.Airstorm)
        {
            Sunny.transform.GetChild(0).gameObject.SetActive(true);
            Rain.transform.GetChild(0).gameObject.SetActive(true);
            Airstrom.transform.GetChild(0).gameObject.SetActive(false);
        }
    }
}